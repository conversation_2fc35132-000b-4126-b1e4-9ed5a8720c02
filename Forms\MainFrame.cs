using System;
using System.Diagnostics;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraBars.Navigation;
using DevExpress.XtraTabbedMdi;

namespace ProManage.Forms
{
    public partial class MainFrame : RibbonForm
    {
        // Current logged in username
        private string _currentUser = "";

        // Sidebar state
        private bool _sidebarExpanded = false;

        // Flag to track if we've already checked for MDI client visibility
        private bool _mdiClientChecked = false;

        // Flag to prevent recursive calls in SplitterMoved event
        private bool _isAdjustingSplitter = false;

        // Flag to prevent recursive calls between AccordionControl StateChanged and SetSidebarState
        private bool _isSettingSidebarState = false;

        // Accordion control elements
        private DevExpress.XtraBars.Navigation.AccordionControlElement _estimateElement;
        private DevExpress.XtraBars.Navigation.AccordionControlElement _databaseElement;
        private DevExpress.XtraBars.Navigation.AccordionControlElement _systemElement;

        // Additional accordion elements
        private DevExpress.XtraBars.Navigation.AccordionControlElement _settingsElement;
        private DevExpress.XtraBars.Navigation.AccordionControlElement _logoutElement;
        private DevExpress.XtraBars.Navigation.AccordionControlElement _exitElement;
        private DevExpress.XtraBars.Navigation.AccordionControlElement _sqlQueryElement;

        /// <summary>
        /// Initializes the form
        /// </summary>
        public MainFrame()
        {
            InitializeComponent();

            // Set up event handlers
            SetupEventHandlers();

            // Set up the marquee effect for the progress bar
            SetupMarqueeProgressBar();

            // Add form load event handler for proper STA thread initialization
            this.Load += MainFrame_Load;
        }

        /// <summary>
        /// Handles the form load event
        /// </summary>
        private void MainFrame_Load(object sender, EventArgs e)
        {
            try
            {
                // Ensure we're running in STA mode
                if (System.Threading.Thread.CurrentThread.GetApartmentState() != System.Threading.ApartmentState.STA)
                {
                    Debug.WriteLine("WARNING: Current thread is not in STA mode!");
                    MessageBox.Show("The application is not running in STA mode. Some features may not work correctly.",
                        "Thread Mode Warning", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    Debug.WriteLine("Confirmed: Current thread is in STA mode.");
                }

                // Disable AllowDrop for MDI client to avoid DragDrop registration issues
                foreach (Control control in this.Controls)
                {
                    if (control is MdiClient)
                    {
                        // Explicitly disable drag and drop for the MdiClient control
                        control.AllowDrop = false;
                        Debug.WriteLine("Disabled AllowDrop for MdiClient control");
                    }

                    // Force handle creation for all controls
                    if (!control.IsHandleCreated)
                    {
                        _ = control.Handle;
                    }
                }

                // Initialize the UI components
                InitializeUI();

                Debug.WriteLine("MainFrame loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in MainFrame_Load: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                MessageBox.Show($"Error initializing MainFrame: {ex.Message}",
                    "Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up the marquee effect for the progress bar
        /// </summary>
        private void SetupMarqueeProgressBar()
        {
            try
            {
                // Configure the MarqueeProgressBarControl
                statusProgressBar.Properties.MarqueeAnimationSpeed = 50;

                // Start the marquee animation
                statusProgressBar.EditValue = 0;

                // Initially hide the progress bar until it's needed
                statusProgressBar.Visible = false;

                // Initialize the ProgressIndicatorService with our progress bar
                ProManage.Modules.UI.ProgressIndicatorService.Instance.Initialize(statusProgressBar);

                Debug.WriteLine("MarqueeProgressBarControl configured successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up marquee progress bar: {ex.Message}");
            }
        }



        /// <summary>
        /// Sets up event handlers for controls
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Note: Button click handlers for ribbon items are already set up in the Designer.cs file
                // We only need to set up handlers for controls that don't have handlers set in the designer

                // SplitContainer splitter moved event

                // SplitContainer splitter moved event
                splitContainerControl.SplitterMoved += SplitContainerControl_SplitterMoved;

                // AccordionControl state changed event
                accordionControl.StateChanged += AccordionControl_StateChanged;

                // AccordionControl click event for hamburger menu
                accordionControl.Click += AccordionControl_Click;

                Debug.WriteLine("Event handlers set up");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the current user for display in the status panel
        /// </summary>
        /// <param name="username">The username to display</param>
        public void SetCurrentUser(string username)
        {
            _currentUser = username;
            lblLoggedInUser.Text = $"Logged in as: {_currentUser}";
        }

        /// <summary>
        /// Initializes UI elements when the form loads
        /// </summary>
        private void InitializeUI()
        {
            try
            {
                Debug.WriteLine("InitializeUI started");

                // Set the form title
                Text = "ProManage 8.0";

                // Debug information about the form
                Debug.WriteLine($"InitializeUI - IsMdiContainer: {IsMdiContainer}");
                Debug.WriteLine($"InitializeUI - Controls count: {Controls.Count}");
                Debug.WriteLine($"InitializeUI - Form Size: {Size}");
                Debug.WriteLine($"InitializeUI - Form Location: {Location}");
                Debug.WriteLine($"InitializeUI - Form WindowState: {WindowState}");
                Debug.WriteLine($"InitializeUI - Form Visible: {Visible}");

                // Log information about all controls
                foreach (Control ctrl in Controls)
                {
                    Debug.WriteLine($"Control: {ctrl.GetType().Name}, Name: {ctrl.Name}, Visible: {ctrl.Visible}, Z-Index: {Controls.GetChildIndex(ctrl)}");
                }

                // Initialize the accordion control first
                InitializeAccordionControl();

                // Ensure the XtraTabbedMdiManager is properly initialized
                InitializeXtraTabbedMdiManager();

                // Set up the ribbon control
                try
                {
                    // Set the form's ribbon control
                    this.Ribbon = ribbonControl;

                    // Additional ribbon settings to match VB.NET version
                    ribbonControl.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.OfficeUniversal;
                    ribbonControl.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
                    ribbonControl.ShowToolbarCustomizeItem = false;
                    ribbonControl.AllowMinimizeRibbon = true;
                    ribbonControl.AllowCustomization = false;
                    ribbonControl.DrawGroupCaptions = DevExpress.Utils.DefaultBoolean.True;
                    ribbonControl.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
                    ribbonControl.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
                    ribbonControl.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Show;

                    // Configure the SplitContainerControl
                    if (splitContainerControl != null)
                    {
                        // Always hide Panel2 as we're not using it
                        splitContainerControl.Panel2.Visible = false;
                        Debug.WriteLine("SplitContainerControl.Panel2 hidden in InitializeUI");

                        // Set the SplitContainerControl to dock to the left
                        splitContainerControl.Dock = DockStyle.Left;
                        Debug.WriteLine("SplitContainerControl docked to left in InitializeUI");
                    }

                    // Ensure the MDI client area is properly visible
                    EnsureMdiClientVisible();

                    // Add a handler for form resize to adjust the MDI client area
                    this.Resize += MainFrame_Resize;

                    // Default to collapsed sidebar state
                    SetSidebarState(false);

                    // TODO: Implement user settings to restore sidebar state
                    // try
                    // {
                    //     // Get the saved state
                    //     bool savedState = Properties.Settings.Default.SidebarExpanded;
                    //
                    //     // Set the sidebar state using our dedicated method
                    //     SetSidebarState(savedState);
                    // }
                    // catch (Exception ex)
                    // {
                    //     // If there's an error, use default collapsed state
                    //     SetSidebarState(false);
                    //
                    //     // Log the error
                    //     Debug.WriteLine($"Error restoring sidebar state: {ex.Message}");
                    // }
                }
                catch (Exception ex)
                {
                    // Log the error but continue
                    Debug.WriteLine($"Error setting up controls: {ex.Message}");
                    Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                }

                // Ensure the form is visible and properly sized
                Visible = true;
                WindowState = FormWindowState.Maximized;
                BringToFront();
                Activate();

                Debug.WriteLine("InitializeUI completed successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeUI: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing UI: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        #region Event Handlers

        /// <summary>
        /// Handles the SplitContainerControl SplitterMoved event to ensure fixed position
        /// </summary>
        private void SplitContainerControl_SplitterMoved(object sender, EventArgs e)
        {
            // Prevent recursive calls
            if (_isAdjustingSplitter)
            {
                return;
            }

            _isAdjustingSplitter = true;
            try
            {
                // Reset the splitter to the fixed position based on sidebar state
                if (_sidebarExpanded)
                {
                    if (splitContainerControl.SplitterPosition != 220)
                    {
                        splitContainerControl.SplitterPosition = 220;
                        splitContainerControl.Width = 220;
                    }
                }
                else
                {
                    if (splitContainerControl.SplitterPosition != 48)
                    {
                        splitContainerControl.SplitterPosition = 48;
                        splitContainerControl.Width = 48;
                    }
                }

                // Find the MDI client control
                Control mdiClient = null;
                foreach (Control ctrl in Controls)
                {
                    if (ctrl is MdiClient)
                    {
                        mdiClient = ctrl;
                        break;
                    }
                }

                // Adjust the MDI client area to account for the sidebar
                if (mdiClient != null)
                {
                    // Set the MDI client bounds to fill the area next to the sidebar
                    mdiClient.Left = splitContainerControl.Right;
                    mdiClient.Top = ribbonControl.Bottom;
                    mdiClient.Width = ClientSize.Width - splitContainerControl.Width;
                    mdiClient.Height = ClientSize.Height - ribbonControl.Height - statusPanel.Height;
                    Debug.WriteLine($"MDI client adjusted after splitter moved: Left={mdiClient.Left}, Top={mdiClient.Top}, Width={mdiClient.Width}, Height={mdiClient.Height}");
                }
            }
            finally
            {
                _isAdjustingSplitter = false;
            }
        }

        /// <summary>
        /// Handles the AccordionControl StateChanged event to ensure proper sizing
        /// </summary>
        private void AccordionControl_StateChanged(object sender, EventArgs e)
        {
            // Prevent recursive calls
            if (_isSettingSidebarState)
            {
                return;
            }

            // Get the current state from the accordion control
            bool newState = accordionControl.OptionsMinimizing.State == DevExpress.XtraBars.Navigation.AccordionControlState.Normal;

            // Only update if the state has actually changed
            if (newState != _sidebarExpanded)
            {
                // Update the sidebar state using our dedicated method
                SetSidebarState(newState);
            }
        }

        /// <summary>
        /// Handles clicks on the AccordionControl to toggle when in minimized state
        /// </summary>
        private void AccordionControl_Click(object sender, EventArgs e)
        {
            try
            {
                // If the sidebar is collapsed and the user clicks on the hamburger menu area,
                // expand the sidebar
                if (!_sidebarExpanded)
                {
                    // Check if the click is in the hamburger menu area (top part of the control)
                    Point mousePos = accordionControl.PointToClient(Control.MousePosition);
                    if (mousePos.Y < 40) // Approximate height of the hamburger icon area
                    {
                        SetSidebarState(true);
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but continue
                Debug.WriteLine($"Error handling accordion click: {ex.Message}");
            }
        }

        /// <summary>
        /// Toggles the sidebar visibility
        /// </summary>
        private void BtnToggleSidebar_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            // Toggle the sidebar state
            SetSidebarState(!_sidebarExpanded);
        }

        /// <summary>
        /// Sets the sidebar state to expanded or collapsed
        /// </summary>
        /// <param name="expanded">True to expand, False to collapse</param>
        private void SetSidebarState(bool expanded)
        {
            // Set the state flag
            _sidebarExpanded = expanded;

            // Suspend layout to prevent flickering
            SuspendLayout();

            // Prevent recursive calls
            _isAdjustingSplitter = true;
            _isSettingSidebarState = true;

            try
            {
                // Ensure the SplitContainerControl is visible
                splitContainerControl.Visible = true;

                // Ensure the accordion control is visible
                accordionControl.Visible = true;

                // Always hide Panel2 as we're not using it
                splitContainerControl.Panel2.Visible = false;

                if (_sidebarExpanded)
                {
                    // Expand the sidebar
                    accordionControl.OptionsMinimizing.State = DevExpress.XtraBars.Navigation.AccordionControlState.Normal;
                    splitContainerControl.SplitterPosition = 220;
                    splitContainerControl.Width = 220;

                    // Ensure the accordion control is properly sized
                    accordionControl.Width = 220;
                    splitContainerControl.Panel1.MinSize = 220;
                }
                else
                {
                    // Collapse the sidebar
                    accordionControl.OptionsMinimizing.State = DevExpress.XtraBars.Navigation.AccordionControlState.Minimized;
                    splitContainerControl.SplitterPosition = 48;
                    splitContainerControl.Width = 48;

                    // Ensure the accordion control is properly sized
                    accordionControl.Width = 48;
                    splitContainerControl.Panel1.MinSize = 48;
                }

                // Force layout updates
                accordionControl.Refresh();
                splitContainerControl.Refresh();

                // Save the state to user settings
                // TODO: Implement user settings
                // My.Settings.SidebarExpanded = _sidebarExpanded;
                // My.Settings.Save();

                Debug.WriteLine($"Sidebar state set to {(_sidebarExpanded ? "expanded" : "collapsed")}");
                Debug.WriteLine($"AccordionControl visible: {accordionControl.Visible}");
                Debug.WriteLine($"AccordionControl width: {accordionControl.Width}");
                Debug.WriteLine($"SplitContainerControl visible: {splitContainerControl.Visible}");
                Debug.WriteLine($"SplitContainerControl width: {splitContainerControl.Width}");
                Debug.WriteLine($"SplitContainerControl position: {splitContainerControl.SplitterPosition}");
                Debug.WriteLine($"SplitContainerControl.Panel2 Visible: {splitContainerControl.Panel2.Visible}");

                // Find the MDI client control
                Control mdiClient = null;
                foreach (Control ctrl in Controls)
                {
                    if (ctrl is MdiClient)
                    {
                        mdiClient = ctrl;
                        break;
                    }
                }

                // Adjust the MDI client area to account for the sidebar
                if (mdiClient != null)
                {
                    // Set the MDI client bounds to fill the area next to the sidebar
                    mdiClient.Left = splitContainerControl.Right;
                    mdiClient.Top = ribbonControl.Bottom;
                    mdiClient.Width = ClientSize.Width - splitContainerControl.Width;
                    mdiClient.Height = ClientSize.Height - ribbonControl.Height - statusPanel.Height;
                    mdiClient.Visible = true;
                    Debug.WriteLine($"MDI client adjusted: Left={mdiClient.Left}, Top={mdiClient.Top}, Width={mdiClient.Width}, Height={mdiClient.Height}");
                }

                // Bring any open child forms to the front
                foreach (Form childForm in MdiChildren)
                {
                    if (childForm.Visible)
                    {
                        childForm.BringToFront();
                        Debug.WriteLine($"Child form {childForm.Name} brought to front after sidebar state change");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log the error but continue
                Debug.WriteLine($"Error setting sidebar state: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
            finally
            {
                // Reset the flags
                _isAdjustingSplitter = false;
                _isSettingSidebarState = false;

                // Resume layout
                ResumeLayout();
            }
        }

        /// <summary>
        /// Handles the User Management button click
        /// </summary>
        private void BtnUserManagement_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // This is a placeholder for future implementation
                // In the future, this will create and open the User Management form
                MessageBox.Show("User Management functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // When the UserForm is implemented, it will look like this:
                // var userForm = new UserForm();
                // OpenChildForm(userForm, "User Management");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening User Management form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Role Management button click
        /// </summary>
        private void BtnRoleManagement_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // This is a placeholder for future implementation
                // In the future, this will create and open the Role Management form
                MessageBox.Show("Role Management functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // When the RoleManagementForm is implemented, it will look like this:
                // var roleManagementForm = new RoleManagementForm();
                // OpenChildForm(roleManagementForm, "Role Management");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Role Management form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Test Form button click
        /// </summary>
        private void BtnTestForm_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // Create and open the Test form using OpenChildForm to prevent duplicates
                var testForm = new TestForm();
                OpenChildForm(testForm, "Test Form");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Test form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Estimate button click
        /// </summary>
        private void BtnEstimate_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                // Create and open the Estimate form using OpenChildForm to prevent duplicates
                Debug.WriteLine("Creating EstimateForm instance");

                var estimateForm = new EstimateForm();

                // Show detailed debugging information
                Debug.WriteLine($"EstimateForm created, Type: {estimateForm.GetType().FullName}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Use OpenChildForm to check for existing forms and open/activate accordingly
                OpenChildForm(estimateForm, "Estimate Management");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnEstimate_ItemClick: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening Estimate form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Database button click
        /// </summary>
        private void BtnDatabase_ItemClick(object sender, EventArgs e)
        {
            try
            {
                // Create and open the Database form using OpenChildForm to prevent duplicates
                var databaseForm = new DatabaseForm();
                OpenChildForm(databaseForm, "Database Configuration");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening Database form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the SQL Query button click
        /// </summary>
        private void BtnSQLQuery_ItemClick(object sender, EventArgs e)
        {
            try
            {
                // Create and open the SQL Query form using OpenChildForm to prevent duplicates
                var sqlQueryForm = new SQLQueryForm();
                OpenChildForm(sqlQueryForm, "SQL Query");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error opening SQL Query form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Settings button click
        /// </summary>
        private void BtnSettings_ItemClick(object sender, EventArgs e)
        {
            // TODO: Open the Settings form
            MessageBox.Show("Settings form not yet implemented", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Handles the Logout button click
        /// </summary>
        private void BtnLogout_ItemClick(object sender, EventArgs e)
        {
            // Confirm logout
            if (MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Close all child forms
                foreach (Form childForm in MdiChildren)
                {
                    childForm.Close();
                }

                // Reset user display
                _currentUser = "";
                lblLoggedInUser.Text = "Logged in as: [Not logged in]";

                // Hide this form and show the login form
                Hide();
                LoginForm loginForm = new LoginForm();
                loginForm.ShowDialog();

                // If the login form is closed, exit the application
                System.Windows.Forms.Application.Exit();
            }
        }

        /// <summary>
        /// Handles the Exit button click
        /// </summary>
        private void BtnExit_ItemClick(object sender, EventArgs e)
        {
            // Confirm exit
            if (MessageBox.Show("Are you sure you want to exit?", "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Close the application
                System.Windows.Forms.Application.Exit();
            }
        }



        #endregion



        #region Accordion Control

        /// <summary>
        /// Initializes the accordion control with navigation elements
        /// </summary>
        private void InitializeAccordionControl()
        {
            // Clear existing elements
            accordionControl.Elements.Clear();

            // Create main group elements to match ribbon structure
            var masterGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acMasterGroup",
                Text = "Master",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            var transactionsGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acTransactionsGroup",
                Text = "Transactions",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            var reportsGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acReportsGroup",
                Text = "Reports",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            var databaseGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acDatabaseGroup",
                Text = "Database",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            var adminGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acAdminGroup",
                Text = "Administration",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            var utilitiesGroupElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acUtilitiesGroup",
                Text = "Utilities",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            // Create Master group elements
            var testFormElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acTestForm",
                Text = "Test Form",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            // Create Transactions group elements
            _estimateElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acEstimate",
                Text = "Estimate",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            var purchaseElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acPurchase",
                Text = "Purchase",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            var salesElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acSales",
                Text = "Sales",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            // Create Database group elements
            _databaseElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acDatabase",
                Text = "Database Configuration",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            _sqlQueryElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acSQLQuery",
                Text = "SQL Query Tool",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            // Create Admin group elements
            var userManagementElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acUserManagement",
                Text = "User Management",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            var roleManagementElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acRoleManagement",
                Text = "Role & Permission Management",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            var parametersElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acParameters",
                Text = "System Parameters",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            // Create System element with child elements
            _systemElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acSystem",
                Text = "System",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Group
            };

            // Create child elements for System group
            _settingsElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acSettings",
                Text = "Settings",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            var toggleSidebarElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acToggleSidebar",
                Text = "Toggle Sidebar",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            _logoutElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acLogout",
                Text = "Logout",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            _exitElement = new DevExpress.XtraBars.Navigation.AccordionControlElement
            {
                Name = "acExit",
                Text = "Exit",
                Style = DevExpress.XtraBars.Navigation.ElementStyle.Item
            };

            // Create separators for visual organization
            var separator1 = new DevExpress.XtraBars.Navigation.AccordionControlSeparator
            {
                Name = "acSeparator1"
            };

            var separator2 = new DevExpress.XtraBars.Navigation.AccordionControlSeparator
            {
                Name = "acSeparator2"
            };

            var separator3 = new DevExpress.XtraBars.Navigation.AccordionControlSeparator
            {
                Name = "acSeparator3"
            };

            var separator4 = new DevExpress.XtraBars.Navigation.AccordionControlSeparator
            {
                Name = "acSeparator4"
            };

            var separator5 = new DevExpress.XtraBars.Navigation.AccordionControlSeparator
            {
                Name = "acSeparator5"
            };

            // Add items to their respective groups
            masterGroupElement.Elements.Add(testFormElement);

            transactionsGroupElement.Elements.Add(_estimateElement);
            transactionsGroupElement.Elements.Add(purchaseElement);
            transactionsGroupElement.Elements.Add(salesElement);

            databaseGroupElement.Elements.Add(_databaseElement);
            databaseGroupElement.Elements.Add(_sqlQueryElement);

            adminGroupElement.Elements.Add(userManagementElement);
            adminGroupElement.Elements.Add(roleManagementElement);
            adminGroupElement.Elements.Add(parametersElement);

            // Add child elements to System group with visual separation
            _systemElement.Elements.Add(toggleSidebarElement);
            _systemElement.Elements.Add(_settingsElement);
            _systemElement.Elements.Add(new DevExpress.XtraBars.Navigation.AccordionControlSeparator { Name = "acSeparatorSettings" });
            _systemElement.Elements.Add(_logoutElement);
            _systemElement.Elements.Add(_exitElement);

            // Add event handlers for elements
            testFormElement.Click += AccordionTestForm_Click;
            _estimateElement.Click += AccordionEstimate_Click;
            purchaseElement.Click += AccordionPurchase_Click;
            salesElement.Click += AccordionSales_Click;
            _databaseElement.Click += AccordionDatabase_Click;
            _sqlQueryElement.Click += AccordionSQLQuery_Click;
            userManagementElement.Click += AccordionUserManagement_Click;
            roleManagementElement.Click += AccordionRoleManagement_Click;
            parametersElement.Click += AccordionParameters_Click;
            toggleSidebarElement.Click += AccordionToggleSidebar_Click;
            _settingsElement.Click += AccordionSettings_Click;
            _logoutElement.Click += AccordionLogout_Click;
            _exitElement.Click += AccordionExit_Click;

            // Add all groups to the accordion control
            accordionControl.Elements.Add(adminGroupElement);
            accordionControl.Elements.Add(separator1);
            accordionControl.Elements.Add(masterGroupElement);
            accordionControl.Elements.Add(separator2);
            accordionControl.Elements.Add(transactionsGroupElement);
            accordionControl.Elements.Add(separator3);
            accordionControl.Elements.Add(reportsGroupElement);
            accordionControl.Elements.Add(separator4);
            accordionControl.Elements.Add(databaseGroupElement);
            accordionControl.Elements.Add(separator5);
            accordionControl.Elements.Add(_systemElement);
            accordionControl.Elements.Add(new DevExpress.XtraBars.Navigation.AccordionControlSeparator { Name = "acSeparator6" });
            accordionControl.Elements.Add(utilitiesGroupElement);

            // Expand all groups by default
            accordionControl.ExpandAll();
        }

        #endregion

        #region XtraTabbedMdiManager

        /// <summary>
        /// Initializes the XtraTabbedMdiManager with proper settings
        /// </summary>
        private void InitializeXtraTabbedMdiManager()
        {
            try
            {
                // Ensure the XtraTabbedMdiManager is properly configured
                xtraTabbedMdiManager.MdiParent = this;
                Debug.WriteLine($"Setting XtraTabbedMdiManager.MdiParent to {Name}");
                Debug.WriteLine($"MainFrame.IsMdiContainer = {IsMdiContainer}");

                // Set additional properties for better appearance
                xtraTabbedMdiManager.AppearancePage.Header.Font = new Font("Segoe UI", 9.0f, FontStyle.Regular, GraphicsUnit.Point);
                xtraTabbedMdiManager.AppearancePage.HeaderActive.Font = new Font("Segoe UI", 9.0f, FontStyle.Bold, GraphicsUnit.Point);
                xtraTabbedMdiManager.AppearancePage.HeaderActive.ForeColor = Color.FromArgb(0, 122, 204);

                // Configure tab behavior
                xtraTabbedMdiManager.ClosePageButtonShowMode = DevExpress.XtraTab.ClosePageButtonShowMode.InActiveTabPageAndTabControlHeader;
                xtraTabbedMdiManager.FloatOnDoubleClick = DevExpress.Utils.DefaultBoolean.True;
                xtraTabbedMdiManager.FloatOnDrag = DevExpress.Utils.DefaultBoolean.True;
                xtraTabbedMdiManager.HeaderButtons = DevExpress.XtraTab.TabButtons.Close;
                xtraTabbedMdiManager.HeaderButtonsShowMode = DevExpress.XtraTab.TabButtonShowMode.Always;

                // Set up event handlers
                xtraTabbedMdiManager.PageAdded += XtraTabbedMdiManager_PageAdded;
                xtraTabbedMdiManager.PageRemoved += XtraTabbedMdiManager_PageRemoved;

                // Add handler for the SelectedPageChanged event to ensure child forms are visible
                xtraTabbedMdiManager.SelectedPageChanged += XtraTabbedMdiManager_PageActivated;

                Debug.WriteLine("XtraTabbedMdiManager initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing XtraTabbedMdiManager: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles the XtraTabbedMdiManager SelectedPageChanged event
        /// </summary>
        private void XtraTabbedMdiManager_PageActivated(object sender, EventArgs e)
        {
            try
            {
                // Get the selected page
                var selectedPage = xtraTabbedMdiManager.SelectedPage;

                if (selectedPage != null)
                {
                    // Get the form associated with the activated page
                    var form = selectedPage.MdiChild;

                    if (form != null)
                    {
                        // Ensure the form is visible
                        form.Visible = true;

                        // Bring the form to the front
                        form.BringToFront();

                        Debug.WriteLine($"MDI Tab Page Activated: {selectedPage.Text}, Form brought to front");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in XtraTabbedMdiManager_PageActivated: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Handles the PageAdded event of the XtraTabbedMdiManager
        /// </summary>
        private void XtraTabbedMdiManager_PageAdded(object sender, EventArgs e)
        {
            // Get the newly added page (most recent page)
            if (xtraTabbedMdiManager.Pages.Count > 0)
            {
                var page = xtraTabbedMdiManager.Pages[xtraTabbedMdiManager.Pages.Count - 1];
                Debug.WriteLine($"MDI Tab Page Added: {page.Text}");
            }
        }

        /// <summary>
        /// Handles the PageRemoved event of the XtraTabbedMdiManager
        /// </summary>
        private void XtraTabbedMdiManager_PageRemoved(object sender, EventArgs e)
        {
            Debug.WriteLine("MDI Tab Page Removed");
        }

        #endregion

        #region Form Management

        /// <summary>
        /// Opens a form as an MDI child
        /// </summary>
        /// <param name="form">The form to open</param>
        /// <param name="formName">The name to display in the tab</param>
        /// <param name="uniqueIdentifier">Optional unique identifier for the form</param>
        /// <returns>The opened form</returns>
        private Form OpenChildForm(Form form, string formName, string uniqueIdentifier = null)
        {
            try
            {
                Debug.WriteLine($"OpenChildForm called for {formName}, Type: {form.GetType().FullName}");

                // Check if the form is already open
                foreach (Form childForm in MdiChildren)
                {
                    // If uniqueIdentifier is provided, check both type and identifier
                    if (childForm.GetType() == form.GetType())
                    {
                        if (!string.IsNullOrEmpty(uniqueIdentifier))
                        {
                            // Check if this form has the same identifier tag
                            if (childForm.Tag != null && childForm.Tag.ToString() == uniqueIdentifier)
                            {
                                // Activate the existing form with matching identifier
                                childForm.Activate();
                                // Find and select the corresponding tab page
                                foreach (DevExpress.XtraTabbedMdi.XtraMdiTabPage page in xtraTabbedMdiManager.Pages)
                                {
                                    if (page.MdiChild == childForm)
                                    {
                                        xtraTabbedMdiManager.SelectedPage = page;
                                        break;
                                    }
                                }
                                Debug.WriteLine($"Activated existing form with identifier: {uniqueIdentifier}");
                                return childForm;
                            }
                        }
                        else
                        {
                            // No identifier provided, just check type
                            // Activate the existing form
                            childForm.Activate();
                            // Find and select the corresponding tab page
                            foreach (DevExpress.XtraTabbedMdi.XtraMdiTabPage page in xtraTabbedMdiManager.Pages)
                            {
                                if (page.MdiChild == childForm)
                                {
                                    xtraTabbedMdiManager.SelectedPage = page;
                                    break;
                                }
                            }
                            Debug.WriteLine($"Activated existing form: {childForm.Text}");
                            return childForm;
                        }
                    }
                }

                // If we get here, the form is not open yet
                Debug.WriteLine($"Opening new form: {formName}, Type: {form.GetType().FullName}");

                // Ensure the MDI client area is properly visible before adding a new form
                ForceEnsureMdiClientVisible();

                // Set the form properties
                form.MdiParent = this;
                form.Text = formName;
                form.WindowState = FormWindowState.Maximized;

                // Ensure the form is properly configured for MDI
                form.FormBorderStyle = FormBorderStyle.None;
                Debug.WriteLine($"Setting MDI properties for form: {form.Name}, Type: {form.GetType().FullName}");

                // If uniqueIdentifier is provided, set it as the form's Tag
                if (!string.IsNullOrEmpty(uniqueIdentifier))
                {
                    form.Tag = uniqueIdentifier;
                }

                try
                {
                    // Show the form
                    Debug.WriteLine($"Calling Form.Show() for {form.Name}");
                    form.Show();
                    Debug.WriteLine($"Form.Show() completed for {form.Name}");

                    // Activate the form to ensure it's visible
                    Debug.WriteLine($"Calling Form.Activate() for {form.Name}");
                    form.Activate();
                    Debug.WriteLine($"Form.Activate() completed for {form.Name}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error showing form: {ex.Message}");
                    Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                    // Show a message box with the error
                    MessageBox.Show($"Error opening {formName}: {ex.Message}\n\nThis form may not be fully implemented yet.",
                                   "Form Error",
                                   MessageBoxButtons.OK,
                                   MessageBoxIcon.Warning);

                    return null;
                }

                // Find the MDI client control
                Control mdiClient = null;
                foreach (Control ctrl in Controls)
                {
                    if (ctrl is MdiClient)
                    {
                        mdiClient = ctrl;
                        break;
                    }
                }

                // Ensure the MDI client is visible and properly positioned
                if (mdiClient != null)
                {
                    mdiClient.Visible = true;
                    Debug.WriteLine($"MDI client visibility ensured: {mdiClient.Visible}");
                }
                else
                {
                    Debug.WriteLine("MDI client not found in Controls collection");
                }

                // Ensure the form is selected in the XtraTabbedMdiManager
                if (xtraTabbedMdiManager.Pages.Count > 0)
                {
                    foreach (DevExpress.XtraTabbedMdi.XtraMdiTabPage page in xtraTabbedMdiManager.Pages)
                    {
                        if (page.MdiChild == form)
                        {
                            xtraTabbedMdiManager.SelectedPage = page;
                            Debug.WriteLine($"Selected page for form: {form.Text}");
                            break;
                        }
                    }
                }

                // Force refresh of the MDI container
                Refresh();
                Debug.WriteLine($"MainFrame.Refresh() called");

                // Additional debug information after showing the form
                Debug.WriteLine($"Form visible after Show: {form.Visible}");
                Debug.WriteLine($"Form MDI Parent after Show: {(form.MdiParent == null ? "None" : form.MdiParent.Name)}");
                Debug.WriteLine($"Form count in MdiChildren after Show: {MdiChildren.Length}");
                Debug.WriteLine($"XtraTabbedMdiManager Pages count: {xtraTabbedMdiManager.Pages.Count}");

                return form;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening form: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");

                // Show a message box with the error
                MessageBox.Show($"Error opening {formName}: {ex.Message}",
                               "Form Error",
                               MessageBoxButtons.OK,
                               MessageBoxIcon.Error);

                // Dispose the form if it wasn't successfully opened
                if (form != null && !form.IsDisposed)
                {
                    form.Dispose();
                }

                return null;
            }
        }

        /// <summary>
        /// Handles the form resize event
        /// </summary>
        private void MainFrame_Resize(object sender, EventArgs e)
        {
            // Ensure the MDI client area is properly visible
            EnsureMdiClientVisible();
        }

        /// <summary>
        /// Ensures that the MDI client area is visible and properly positioned
        /// </summary>
        private void EnsureMdiClientVisible()
        {
            // Only run this once per session unless forced
            if (_mdiClientChecked)
            {
                return;
            }

            try
            {
                // Find the MDI client control
                Control mdiClient = null;
                foreach (Control ctrl in Controls)
                {
                    if (ctrl is MdiClient)
                    {
                        mdiClient = ctrl;
                        break;
                    }
                }

                if (mdiClient != null)
                {
                    // Ensure the MDI client is visible
                    mdiClient.Visible = true;

                    // Set the MDI client to be at the correct Z-order
                    // We want it above the ribbon but below other controls
                    Controls.SetChildIndex(mdiClient, 1);
                    Debug.WriteLine("MDI client Z-order set");

                    // Adjust the MDI client area to account for the sidebar
                    mdiClient.Left = splitContainerControl.Right;
                    mdiClient.Top = ribbonControl.Bottom;
                    mdiClient.Width = ClientSize.Width - splitContainerControl.Width;
                    mdiClient.Height = ClientSize.Height - ribbonControl.Height - statusPanel.Height;
                    Debug.WriteLine($"MDI client adjusted: Left={mdiClient.Left}, Top={mdiClient.Top}, Width={mdiClient.Width}, Height={mdiClient.Height}");

                    // Always hide Panel2 as we're not using it
                    if (splitContainerControl != null)
                    {
                        splitContainerControl.Panel2.Visible = false;
                        Debug.WriteLine("SplitContainerControl.Panel2 hidden");
                    }

                    // Force a refresh of the form to update the layout
                    Refresh();
                    Debug.WriteLine("Form refreshed to update layout");

                    // Log information about the controls
                    Debug.WriteLine($"MDI client bounds: {mdiClient.Bounds}");
                    Debug.WriteLine($"MDI client visible: {mdiClient.Visible}");
                    Debug.WriteLine($"MDI client Z-index: {Controls.GetChildIndex(mdiClient)}");
                    Debug.WriteLine($"SplitContainerControl visible: {splitContainerControl.Visible}");
                    Debug.WriteLine($"SplitContainerControl width: {splitContainerControl.Width}");
                    Debug.WriteLine($"SplitContainerControl Z-index: {Controls.GetChildIndex(splitContainerControl)}");
                    Debug.WriteLine($"SplitContainerControl.Panel2 Visible: {splitContainerControl.Panel2.Visible}");
                    Debug.WriteLine($"AccordionControl visible: {accordionControl.Visible}");
                    Debug.WriteLine($"MDI children count: {MdiChildren.Length}");

                    // Set the flag to indicate we've checked the MDI client
                    _mdiClientChecked = true;
                }
                else
                {
                    Debug.WriteLine("MDI client control not found");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EnsureMdiClientVisible: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
            }
        }

        /// <summary>
        /// Forces the MDI client area to be visible and properly positioned
        /// </summary>
        private void ForceEnsureMdiClientVisible()
        {
            // Reset the flag to force a check
            _mdiClientChecked = false;

            // Call the regular method
            EnsureMdiClientVisible();

            // Make sure the SplitContainerControl is visible
            if (splitContainerControl != null)
            {
                splitContainerControl.Visible = true;

                // Ensure the accordion control is visible
                if (accordionControl != null)
                {
                    accordionControl.Visible = true;
                    Debug.WriteLine("AccordionControl visibility ensured");
                }

                // Always hide Panel2 as we're not using it
                splitContainerControl.Panel2.Visible = false;
                Debug.WriteLine("SplitContainerControl.Panel2 hidden");
            }

            // Find the MDI client control again to ensure it's properly positioned
            Control mdiClient = null;
            foreach (Control ctrl in Controls)
            {
                if (ctrl is MdiClient)
                {
                    mdiClient = ctrl;
                    break;
                }
            }

            // Adjust the MDI client area again to ensure it's properly positioned
            if (mdiClient != null)
            {
                mdiClient.Left = splitContainerControl.Right;
                mdiClient.Top = ribbonControl.Bottom;
                mdiClient.Width = ClientSize.Width - splitContainerControl.Width;
                mdiClient.Height = ClientSize.Height - ribbonControl.Height - statusPanel.Height;
                mdiClient.Visible = true;
                Debug.WriteLine($"MDI client re-adjusted: Left={mdiClient.Left}, Top={mdiClient.Top}, Width={mdiClient.Width}, Height={mdiClient.Height}");
            }

            // Bring any open child forms to the front
            foreach (Form childForm in MdiChildren)
            {
                if (childForm.Visible)
                {
                    childForm.BringToFront();
                    Debug.WriteLine($"Child form {childForm.Name} brought to front");
                }
            }
        }

        #endregion

        #region Accordion Element Click Event Handlers

        /// <summary>
        /// Handles the Test Form accordion element click
        /// </summary>
        private void AccordionTestForm_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionTestForm_Click called with EventArgs");
                // Create and open the Test form
                var testForm = new TestForm();
                Debug.WriteLine("Creating TestForm instance from accordion");

                // Try to open the form
                var openedForm = OpenChildForm(testForm, "Test Form");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionTestForm_Click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening Test form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Estimate accordion element click
        /// </summary>
        private void AccordionEstimate_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionEstimate_Click called with EventArgs");
                // Create and open the Estimate form
                var estimateForm = new EstimateForm();
                Debug.WriteLine("Creating EstimateForm instance from accordion");

                // Show detailed debugging information
                Debug.WriteLine($"EstimateForm created, Type: {estimateForm.GetType().FullName}");
                Debug.WriteLine($"Current MDI Parent: {(estimateForm.MdiParent == null ? "None" : estimateForm.MdiParent.Name)}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Try to open the form
                var openedForm = OpenChildForm(estimateForm, "Estimate");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
                if (openedForm != null)
                {
                    Debug.WriteLine($"Form MDI Parent after open: {(openedForm.MdiParent == null ? "None" : openedForm.MdiParent.Name)}");
                    Debug.WriteLine($"Form visible: {openedForm.Visible}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionEstimate_Click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening Estimate form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Database accordion element click
        /// </summary>
        private void AccordionDatabase_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionDatabase_Click called with EventArgs");
                // Create and open the Database form
                var databaseForm = new DatabaseForm();
                Debug.WriteLine("Creating DatabaseForm instance from accordion");

                // Show detailed debugging information
                Debug.WriteLine($"DatabaseForm created, Type: {databaseForm.GetType().FullName}");
                Debug.WriteLine($"Current MDI Parent: {(databaseForm.MdiParent == null ? "None" : databaseForm.MdiParent.Name)}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Try to open the form
                var openedForm = OpenChildForm(databaseForm, "Database Configuration");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
                if (openedForm != null)
                {
                    Debug.WriteLine($"Form MDI Parent after open: {(openedForm.MdiParent == null ? "None" : openedForm.MdiParent.Name)}");
                    Debug.WriteLine($"Form visible: {openedForm.Visible}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionDatabase_Click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening Database form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the SQL Query accordion element click
        /// </summary>
        private void AccordionSQLQuery_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionSQLQuery_Click called with EventArgs");
                // Create and open the SQL Query form
                var sqlQueryForm = new SQLQueryForm();
                Debug.WriteLine("Creating SQLQueryForm instance from accordion");

                // Show detailed debugging information
                Debug.WriteLine($"SQLQueryForm created, Type: {sqlQueryForm.GetType().FullName}");
                Debug.WriteLine($"Current MDI Parent: {(sqlQueryForm.MdiParent == null ? "None" : sqlQueryForm.MdiParent.Name)}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Try to open the form
                var openedForm = OpenChildForm(sqlQueryForm, "SQL Query Tool");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
                if (openedForm != null)
                {
                    Debug.WriteLine($"Form MDI Parent after open: {(openedForm.MdiParent == null ? "None" : openedForm.MdiParent.Name)}");
                    Debug.WriteLine($"Form visible: {openedForm.Visible}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionSQLQuery_Click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening SQL Query form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the User Management accordion element click
        /// </summary>
        private void AccordionUserManagement_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionUserManagement_Click called with EventArgs");
                // This is a placeholder for future implementation
                // In the future, this will create and open the User Management form
                MessageBox.Show("User Management functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // When the UserForm is implemented, it will look like this:
                // var userForm = new UserForm();
                // OpenChildForm(userForm, "User Management");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionUserManagement_Click: {ex.Message}");
                MessageBox.Show($"Error opening User Management form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Role Management accordion element click
        /// </summary>
        private void AccordionRoleManagement_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionRoleManagement_Click called with EventArgs");
                // This is a placeholder for future implementation
                // In the future, this will create and open the Role Management form
                MessageBox.Show("Role Management functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // When the RoleForm is implemented, it will look like this:
                // var roleForm = new RoleForm();
                // OpenChildForm(roleForm, "Role Management");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionRoleManagement_Click: {ex.Message}");
                MessageBox.Show($"Error opening Role Management form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Settings accordion element click
        /// </summary>
        private void AccordionSettings_Click(object sender, EventArgs e)
        {
            Debug.WriteLine("AccordionSettings_Click called with EventArgs");
            // Placeholder for future Settings form
            MessageBox.Show("Settings functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// Handles the Logout accordion element click
        /// </summary>
        private void AccordionLogout_Click(object sender, EventArgs e)
        {
            Debug.WriteLine("AccordionLogout_Click called with EventArgs");
            // Confirm logout
            if (MessageBox.Show("Are you sure you want to logout?", "Confirm Logout", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Close all child forms
                foreach (Form childForm in MdiChildren)
                {
                    childForm.Close();
                }

                // Reset user display
                _currentUser = "";
                lblLoggedInUser.Text = "Logged in as: [Not logged in]";

                // Hide this form and show the login form
                Hide();
                var loginForm = new LoginForm();
                loginForm.ShowDialog();

                // If the login form is closed, exit the application
                Environment.Exit(0);
            }
        }

        /// <summary>
        /// Handles the Exit accordion element click
        /// </summary>
        private void AccordionExit_Click(object sender, EventArgs e)
        {
            Debug.WriteLine("AccordionExit_Click called with EventArgs");
            // Confirm exit
            if (MessageBox.Show("Are you sure you want to exit the application?", "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
            {
                // Close the application
                Environment.Exit(0);
            }
        }

        /// <summary>
        /// Handles the Purchase accordion element click
        /// </summary>
        private void AccordionPurchase_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionPurchase_Click called with EventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Purchase functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionPurchase_Click: {ex.Message}");
                MessageBox.Show($"Error opening Purchase form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Sales accordion element click
        /// </summary>
        private void AccordionSales_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionSales_Click called with EventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Sales functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionSales_Click: {ex.Message}");
                MessageBox.Show($"Error opening Sales form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Parameters accordion element click
        /// </summary>
        private void AccordionParameters_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("AccordionParameters_Click called with EventArgs");
                // Create and open the Parameters form
                var parametersForm = new ParametersForm();
                Debug.WriteLine("Creating ParametersForm instance from accordion");

                // Show detailed debugging information
                Debug.WriteLine($"ParametersForm created, Type: {parametersForm.GetType().FullName}");
                Debug.WriteLine($"Current MDI Parent: {(parametersForm.MdiParent == null ? "None" : parametersForm.MdiParent.Name)}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Try to open the form
                var openedForm = OpenChildForm(parametersForm, "System Parameters");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
                if (openedForm != null)
                {
                    Debug.WriteLine($"Form MDI Parent after open: {(openedForm.MdiParent == null ? "None" : openedForm.MdiParent.Name)}");
                    Debug.WriteLine($"Form visible: {openedForm.Visible}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AccordionParameters_Click: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening System Parameters form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Parameters button click
        /// </summary>
        private void BtnParams_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnParams_ItemClick called with ItemClickEventArgs");
                // Create and open the Parameters form
                var parametersForm = new ParametersForm();
                Debug.WriteLine("Creating ParametersForm instance from ribbon button");

                // Show detailed debugging information
                Debug.WriteLine($"ParametersForm created, Type: {parametersForm.GetType().FullName}");
                Debug.WriteLine($"Current MDI Parent: {(parametersForm.MdiParent == null ? "None" : parametersForm.MdiParent.Name)}");
                Debug.WriteLine($"MainFrame is MDI Container: {IsMdiContainer}");

                // Try to open the form
                var openedForm = OpenChildForm(parametersForm, "System Parameters");

                // Check if form was opened successfully
                Debug.WriteLine($"Form opened successfully: {openedForm != null}");
                if (openedForm != null)
                {
                    Debug.WriteLine($"Form MDI Parent after open: {(openedForm.MdiParent == null ? "None" : openedForm.MdiParent.Name)}");
                    Debug.WriteLine($"Form visible: {openedForm.Visible}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnParams_ItemClick: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error opening System Parameters form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Purchase button click
        /// </summary>
        private void BtnPurchase_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnPurchase_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Purchase functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnPurchase_ItemClick: {ex.Message}");
                MessageBox.Show($"Error opening Purchase form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Sales button click
        /// </summary>
        private void BtnSales_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnSales_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Sales functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnSales_ItemClick: {ex.Message}");
                MessageBox.Show($"Error opening Sales form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Toggle Sidebar accordion element click
        /// </summary>
        private void AccordionToggleSidebar_Click(object sender, EventArgs e)
        {
            Debug.WriteLine("AccordionToggleSidebar_Click called with EventArgs");
            // Toggle the sidebar state
            SetSidebarState(!_sidebarExpanded);
        }

        /// <summary>
        /// Handles the About button click
        /// </summary>
        private void BtnAbout_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnAbout_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("ProManage 8.0\nVersion 8.0.0\n\nCopyright © 2023", "About ProManage", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnAbout_ItemClick: {ex.Message}");
                MessageBox.Show($"Error showing About dialog: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Refresh Cache button click
        /// </summary>
        private void BtnRefreshCache_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnRefreshCache_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Cache refresh functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnRefreshCache_ItemClick: {ex.Message}");
                MessageBox.Show($"Error refreshing cache: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Help button click
        /// </summary>
        private void BtnHelp_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnHelp_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Help functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnHelp_ItemClick: {ex.Message}");
                MessageBox.Show($"Error showing Help: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Reports button click
        /// </summary>
        private void BtnReports_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnReports_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Reports functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnReports_ItemClick: {ex.Message}");
                MessageBox.Show($"Error showing Reports: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Option 1 button click
        /// </summary>
        private void BtnOption1_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnOption1_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Option 1 functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnOption1_ItemClick: {ex.Message}");
                MessageBox.Show($"Error executing Option 1: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles the Option 2 button click
        /// </summary>
        private void BtnOption2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("BtnOption2_ItemClick called with ItemClickEventArgs");
                // This is a placeholder for future implementation
                MessageBox.Show("Option 2 functionality will be implemented in a future update.", "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in BtnOption2_ItemClick: {ex.Message}");
                MessageBox.Show($"Error executing Option 2: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion
    }
}
