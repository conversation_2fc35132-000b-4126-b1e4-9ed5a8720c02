-- SaveUser.sql
-- Contains queries for saving user information

-- [InsertUser] --
INSERT INTO users (
    username,
    password_hash,
    full_name,
    email,
    role,
    is_active,
    created_date,
    modified_date
) VALUES (
    @username,
    @password_hash,
    @full_name,
    @email,
    @role,
    @is_active,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
RETURNING user_id;
-- [End] --

-- [UpdateUser] --
UPDATE users
SET 
    password_hash = @password_hash,
    full_name = @full_name,
    email = @email,
    role = @role,
    is_active = @is_active,
    modified_date = CURRENT_TIMESTAMP
WHERE 
    user_id = @user_id;
-- [End] --

-- [UpdateLastLogin] --
UPDATE users
SET 
    last_login_date = CURRENT_TIMESTAMP
WHERE 
    user_id = @user_id;
-- [End] --
