using System;
using System.Windows.Forms;
using DevExpress.XtraReports.UserDesigner;
using Microsoft.Web.WebView2.Core;
using ProManage.Modules.Connections;
using System.Data;
using Npgsql;

namespace ProManage.Forms
{
    /// <summary>
    /// Test form for development purposes
    /// Contains WebView2 control for web content and DevExpress report designer components
    /// </summary>
    public partial class TestForm : Form
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public TestForm()
        {
            InitializeComponent();

            // Set up the form
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.ShowIcon = true;
            this.Text = "Test Form";

            // Add event handlers
            this.Load += TestForm_Load;
        }

        /// <summary>
        /// Handles the form load event
        /// Initializes the WebView2 control and loads HTML content
        /// </summary>
        private async void TestForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize WebView2
                await WebView21.EnsureCoreWebView2Async();

                // Test database connection
                TestDatabaseConnection();

                // Load your local HTML file (React UI)
                // Note: Update this path to a valid local HTML file path
                string htmlPath = "file:///D:/Projects/todo-app/build/index.html";
                WebView21.Source = new Uri(htmlPath);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading form: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Tests the database connection using the updated connection string
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                // Get configuration from settings
                var settings = Modules.Helpers.ConfigurationHelper.LoadDatabaseSettings();

                if (settings.Count == 0)
                {
                    MessageBox.Show("Database settings not found. Please configure the database connection first.",
                        "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string server = settings["Host"];
                string port = settings.ContainsKey("Port") ? settings["Port"] : "5432";
                string database = settings["Database"];
                string username = settings["Username"];
                string password = settings["Password"];

                // Try the alternate connection test that avoids the Microsoft.EE.AsyncInterface dependency
                if (!TryAlternateConnectionTest(server, port, database, username, password))
                {
                    // If alternate method fails, try the original method as fallback
                    string errorMessage;
                    bool success = DatabaseConnectionManager.Instance.TestConnection(
                        server, port, database, username, password, out errorMessage);

                    if (success)
                    {
                        MessageBox.Show("Database connection test successful!",
                            "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Try to execute a simple query
                        using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                        {
                            conn.Open();
                            using (var cmd = new NpgsqlCommand("SELECT current_timestamp", conn))
                            {
                                var result = cmd.ExecuteScalar();
                                MessageBox.Show($"Query executed successfully!\nCurrent server time: {result}",
                                    "Query Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                    }
                    else
                    {
                        MessageBox.Show($"Database connection test failed:\n{errorMessage}",
                            "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error testing database connection: {ex.Message}",
                    "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Alternative connection test method that doesn't rely on Microsoft.EE.AsyncInterface
        /// </summary>
        /// <returns>True if connection test was successful</returns>
        private bool TryAlternateConnectionTest(string server, string port, string database, string username, string password)
        {
            try
            {
                // Build connection string directly
                string connectionString = $"Host={server};Port={port};Database={database};Username={username};Password={password};";

                // Create connection directly without the manager
                using (var connection = new NpgsqlConnection(connectionString))
                {
                    // Open connection
                    connection.Open();

                    // Test a simple query
                    using (var command = new NpgsqlCommand("SELECT current_timestamp", connection))
                    {
                        var result = command.ExecuteScalar();
                        MessageBox.Show($"Connection successful!\nServer time: {result}",
                            "Direct Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    return true;
                }
            }
            catch (NpgsqlException ex)
            {
                MessageBox.Show($"Direct connection test failed:\n{ex.Message}",
                    "Connection Test", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in direct connection test: {ex.Message}",
                    "Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
    }
}
