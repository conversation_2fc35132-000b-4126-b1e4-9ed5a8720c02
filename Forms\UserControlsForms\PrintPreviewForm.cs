﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraReports.UI;
using System.Diagnostics;

namespace ProManage.Forms.UserControlsForms
{
    public partial class PrintPreviewForm : Form
    {
        private XtraReport currentReport;

        public PrintPreviewForm()
        {
            InitializeComponent();
            SetupEventHandlers();
        }

        /// <summary>
        /// Sets up event handlers for toolbar buttons
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Wire up toolbar button events
                btnPrint.ItemClick += BtnPrint_ItemClick;
                btnPrintPreview.ItemClick += BtnPrintPreview_ItemClick;
                btnFind.ItemClick += BtnFind_ItemClick;
                barButtonItem3.ItemClick += BtnExport_ItemClick; // Export button

                Debug.WriteLine("PrintPreviewForm event handlers setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up PrintPreviewForm event handlers: {ex.Message}");
                MessageBox.Show($"Error initializing print preview form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Loads and displays a report in the document viewer
        /// </summary>
        /// <param name="report">The XtraReport to display</param>
        /// <param name="reportTitle">Optional title for the form</param>
        public void LoadReport(XtraReport report, string reportTitle = "Print Preview")
        {
            try
            {
                Debug.WriteLine($"=== LoadReport: Starting for {reportTitle} ===");

                if (report == null)
                {
                    throw new ArgumentNullException(nameof(report), "Report cannot be null");
                }

                // Store reference to current report
                currentReport = report;

                // Update form title
                this.Text = reportTitle;

                // Generate the document
                Debug.WriteLine("Creating document...");
                report.CreateDocument();

                // Display in document viewer
                Debug.WriteLine("Setting document source...");
                documentViewer1.DocumentSource = report;

                Debug.WriteLine($"=== LoadReport: Completed for {reportTitle} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadReport: {ex.Message}");
                MessageBox.Show($"Error loading report: {ex.Message}", "Report Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles print button click
        /// </summary>
        private void BtnPrint_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Print button clicked");

                if (currentReport == null)
                {
                    MessageBox.Show("No report is currently loaded.", "No Report",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Print the current report
                currentReport.Print();
                Debug.WriteLine("Report sent to printer");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error printing report: {ex.Message}");
                MessageBox.Show($"Error printing report: {ex.Message}", "Print Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles print preview button click (refresh preview)
        /// </summary>
        private void BtnPrintPreview_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Print preview refresh button clicked");

                if (currentReport == null)
                {
                    MessageBox.Show("No report is currently loaded.", "No Report",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Refresh the preview
                currentReport.CreateDocument();
                documentViewer1.DocumentSource = currentReport;
                Debug.WriteLine("Preview refreshed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing preview: {ex.Message}");
                MessageBox.Show($"Error refreshing preview: {ex.Message}", "Preview Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles find button click
        /// </summary>
        private void BtnFind_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Find button clicked");
                // Use document viewer's built-in search functionality
                // Note: DevExpress DocumentViewer has built-in search via Ctrl+F
                // We can trigger this programmatically or show a message
                MessageBox.Show("Use Ctrl+F to search within the document.", "Search",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing search panel: {ex.Message}");
                MessageBox.Show($"Error showing search: {ex.Message}", "Search Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles export button click
        /// </summary>
        private void BtnExport_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            try
            {
                Debug.WriteLine("Export button clicked");

                if (currentReport == null)
                {
                    MessageBox.Show("No report is currently loaded.", "No Report",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Show save file dialog for export
                using (var saveDialog = new SaveFileDialog())
                {
                    saveDialog.Filter = "PDF Files (*.pdf)|*.pdf|Excel Files (*.xlsx)|*.xlsx|Word Files (*.docx)|*.docx|All Files (*.*)|*.*";
                    saveDialog.DefaultExt = "pdf";
                    saveDialog.AddExtension = true;
                    saveDialog.Title = "Export Report";

                    if (saveDialog.ShowDialog() == DialogResult.OK)
                    {
                        string extension = System.IO.Path.GetExtension(saveDialog.FileName).ToLower();

                        switch (extension)
                        {
                            case ".pdf":
                                currentReport.ExportToPdf(saveDialog.FileName);
                                break;
                            case ".xlsx":
                                currentReport.ExportToXlsx(saveDialog.FileName);
                                break;
                            case ".docx":
                                currentReport.ExportToDocx(saveDialog.FileName);
                                break;
                            default:
                                currentReport.ExportToPdf(saveDialog.FileName);
                                break;
                        }

                        MessageBox.Show($"Report exported successfully to:\n{saveDialog.FileName}", "Export Complete",
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                Debug.WriteLine("Export operation completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing export dialog: {ex.Message}");
                MessageBox.Show($"Error exporting report: {ex.Message}", "Export Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Clean up resources when form is closing
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            try
            {
                // Clean up report reference
                currentReport = null;

                // Clear document viewer
                if (documentViewer1 != null)
                {
                    documentViewer1.DocumentSource = null;
                }

                Debug.WriteLine("PrintPreviewForm resources cleaned up");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error cleaning up PrintPreviewForm: {ex.Message}");
            }

            base.OnFormClosing(e);
        }
    }
}
