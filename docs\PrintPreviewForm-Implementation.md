# PrintPreviewForm Implementation

## Overview
The PrintPreviewForm is a centralized print preview system for the ProManage application. It provides a reusable form that can display reports from any module in the system.

## Architecture

### PrintPreviewForm Features
- **Document Viewer**: Uses DevExpress DocumentViewer for professional report display
- **Toolbar Controls**: Print, Print Preview (refresh), Find, Zoom, and Export functionality
- **Reusable Design**: Can be called from any form with any XtraReport
- **Error Handling**: Comprehensive error handling and user feedback
- **Resource Management**: Proper cleanup of resources when form closes

### Key Methods

#### LoadReport(XtraReport report, string reportTitle)
- Accepts any DevExpress XtraReport for display
- Sets the form title dynamically
- Generates the document and displays it in the viewer
- Handles errors gracefully with user feedback

#### Toolbar Event Handlers
- **Print**: Sends the current report directly to printer
- **Print Preview**: Refreshes the current report preview
- **Find**: Provides search instructions (Ctrl+F for built-in search)
- **Export**: Opens save dialog with PDF, Excel, and Word export options

## Integration with EstimateForm

### Button Configuration
The EstimateForm's `BarButtonPrintPreview` button is wired to call the `ShowPrintPreview()` method through:
1. Event handler in `SetupButtonEvents()` method
2. Fallback handler in `barButtonItem2_ItemClick()` method (designer assignment)

### ShowPrintPreview() Method
1. Validates that an estimate is currently loaded
2. Uses existing `EstimateReportService.CreateEstimateReport()` to generate report
3. Creates new PrintPreviewForm instance
4. Loads the report with descriptive title
5. Shows the form as modal dialog

## Usage Pattern

```csharp
// Create the print preview form
var printPreviewForm = new PrintPreviewForm();

// Generate your report (any XtraReport)
var report = YourReportService.CreateReport(data);

// Load and display the report
string title = "Your Report Title";
printPreviewForm.LoadReport(report, title);

// Show the form
printPreviewForm.ShowDialog(parentForm);
```

## Benefits

### Centralized Print Preview
- Single form handles all print preview functionality
- Consistent user experience across all modules
- Reduces code duplication

### Reusable Architecture
- Can be used by any form in the ProManage system
- Accepts any DevExpress XtraReport
- Standardized toolbar functionality

### Professional Features
- Full DevExpress document viewer capabilities
- Print, export, search, and zoom functionality
- Proper error handling and resource management

## Future Enhancements

### Planned Features
1. **Multi-Report Support**: Ability to load multiple reports in tabs
2. **Print Settings**: Advanced print configuration options
3. **Report History**: Recently viewed reports functionality
4. **Custom Export Options**: Additional export formats and settings

### Integration Points
- Can be extended for other forms (Invoice, Purchase Order, etc.)
- Compatible with existing backstage view functionality
- Maintains separation from form-specific logic

## Technical Notes

### Dependencies
- DevExpress.XtraReports.UI
- DevExpress.XtraPrinting.Preview
- System.Diagnostics (for logging)

### Error Handling
- Null report validation
- Document generation error handling
- Print/export operation error handling
- Resource cleanup on form closing

### Performance Considerations
- Reports are generated on-demand
- Document viewer handles large reports efficiently
- Memory cleanup prevents resource leaks
