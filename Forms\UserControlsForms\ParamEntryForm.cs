using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Data.ParametersForm;
using System.Diagnostics;

namespace ProManage.Forms
{
    /// <summary>
    /// Parameter Entry Form - Popup dialog for adding new parameters or editing existing ones
    /// </summary>
    public partial class ParamEntryForm : Form
    {
        /// <summary>
        /// Gets the newly created parameter ID after successful save
        /// </summary>
        public int NewParameterId { get; private set; }

        /// <summary>
        /// Gets or sets the parameter being edited (null for new parameters)
        /// </summary>
        private ParametersFormModel EditingParameter { get; set; }

        /// <summary>
        /// Gets whether this form is in edit mode
        /// </summary>
        public bool IsEditMode => EditingParameter != null;

        public ParamEntryForm()
        {
            InitializeComponent();
            SetupForm();
        }

        /// <summary>
        /// Sets up form properties and behavior
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // Form properties
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.ShowInTaskbar = false;
                this.StartPosition = FormStartPosition.CenterParent;
                this.Text = "Add New Parameter";
                this.Size = new Size(400, 220);

                // Set tab order
                txtParameterCode.TabIndex = 0;
                txtParameterValue.TabIndex = 1;
                txtPurpose.TabIndex = 2;
                btnSave.TabIndex = 3;
                btnCancel.TabIndex = 4;

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine("ParamEntryForm setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupForm: {ex.Message}");
                MessageBox.Show($"Error setting up form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up the form for editing an existing parameter
        /// </summary>
        /// <param name="parameter">The parameter to edit</param>
        public void SetupForEdit(ParametersFormModel parameter)
        {
            try
            {
                Debug.WriteLine($"=== SetupForEdit: Setting up form for parameter ID {parameter.Id} ===");

                // Store the parameter being edited
                EditingParameter = parameter;

                // Change form title
                this.Text = "Edit Parameter";

                // Pre-populate the fields
                txtParameterCode.Text = parameter.ParameterCode ?? "";
                txtParameterValue.Text = parameter.ParameterValue ?? "";
                txtPurpose.Text = parameter.Purpose ?? "";

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine($"Form setup for editing parameter: {parameter.ParameterCode}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up form for edit: {ex.Message}");
                MessageBox.Show($"Error setting up edit form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Save button click - validates and saves parameter
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Save button clicked ===");

                // Validate input fields
                if (!ValidateInput())
                {
                    return; // Validation failed, stay on form
                }

                if (IsEditMode)
                {
                    // Update existing parameter
                    EditingParameter.ParameterCode = txtParameterCode.Text.Trim();
                    EditingParameter.ParameterValue = txtParameterValue.Text.Trim();
                    EditingParameter.Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim();
                    EditingParameter.ModifiedAt = DateTime.Now;

                    // Validate model
                    if (!EditingParameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Update in database
                    bool success = ParametersFormRepository.UpdateParameter(EditingParameter);
                    if (!success)
                    {
                        MessageBox.Show("Failed to update parameter. Please try again.", "Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    Debug.WriteLine($"Parameter updated successfully: ID {EditingParameter.Id}");

                    // Show success message
                    MessageBox.Show("Parameter updated successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Create new parameter
                    var parameter = new ParametersFormModel
                    {
                        ParameterCode = txtParameterCode.Text.Trim(),
                        ParameterValue = txtParameterValue.Text.Trim(),
                        Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim()
                    };

                    // Validate model
                    if (!parameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Save to database
                    int newId = ParametersFormRepository.InsertParameter(parameter);
                    NewParameterId = newId;

                    Debug.WriteLine($"Parameter saved successfully with ID: {newId}");

                    // Show success message
                    MessageBox.Show("Parameter saved successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Close form with OK result
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving parameter: {ex.Message}");
                MessageBox.Show($"Error saving parameter: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click - closes form without saving
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Cancel button clicked");
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button: {ex.Message}");
                this.Close(); // Force close even if error
            }
        }

        /// <summary>
        /// Validates input fields before saving
        /// </summary>
        /// <returns>True if validation passes, false otherwise</returns>
        private bool ValidateInput()
        {
            try
            {
                // Check parameter code (mandatory)
                if (string.IsNullOrWhiteSpace(txtParameterCode.Text))
                {
                    MessageBox.Show("Parameter Code is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter code length
                if (txtParameterCode.Text.Trim().Length > 100)
                {
                    MessageBox.Show("Parameter Code cannot exceed 100 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter value (mandatory)
                if (string.IsNullOrWhiteSpace(txtParameterValue.Text))
                {
                    MessageBox.Show("Parameter Value is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterValue.Focus();
                    return false;
                }

                // Check parameter value length
                if (txtParameterValue.Text.Trim().Length > 255)
                {
                    MessageBox.Show("Parameter Value cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterValue.Focus();
                    return false;
                }

                // Check purpose length (optional field)
                if (!string.IsNullOrWhiteSpace(txtPurpose.Text) && txtPurpose.Text.Trim().Length > 255)
                {
                    MessageBox.Show("Purpose cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPurpose.Focus();
                    return false;
                }

                // Check parameter code uniqueness
                string parameterCode = txtParameterCode.Text.Trim();
                int? excludeId = IsEditMode ? EditingParameter.Id : (int?)null;

                if (ParametersFormRepository.CheckParameterCodeExists(parameterCode, excludeId))
                {
                    MessageBox.Show($"Parameter Code '{parameterCode}' already exists. Please use a different code.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                Debug.WriteLine("Input validation passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateInput: {ex.Message}");
                MessageBox.Show($"Error validating input: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Handles Enter key press to trigger Save button
        /// </summary>
        protected override bool ProcessDialogKey(Keys keyData)
        {
            if (keyData == Keys.Enter && !txtPurpose.Focused)
            {
                BtnSave_Click(this, EventArgs.Empty);
                return true;
            }
            else if (keyData == Keys.Escape)
            {
                BtnCancel_Click(this, EventArgs.Empty);
                return true;
            }

            return base.ProcessDialogKey(keyData);
        }
    }
}
